// --- 修正点在这里 ---
// 将原来的:
// const MongoClient = require('mongodb');
// 修改为:
const { MongoClient } = require('mongodb');
// -------------------

const { randomBytes } = require('crypto'); // 引入 crypto 模块用于生成随机字符串

// --- 直接硬编码的配置信息 ---

// 1. 管理员连接字符串 (用于创建用户和数据库)
const adminConnectionString = "mongodb://root:<EMAIL>:27017/?replicaSet=custom-db-dev-mongodb";

// 2. 生成随机的应用数据库名和用户名
// 它们将是相同的, 例如: "app_db_feb6be39"
const randomSuffix = randomBytes(4).toString('hex'); // 生成8位随机字符串
const appDatabase = `app_db_${randomSuffix}`;
const appUser = appDatabase; // 用户名和数据库名保持一致
const appPassword = "Au46ab"; // 你可以为这个密码也设置一个随机生成逻辑

// --- 辅助函数：生成随机字符串 ---
// (如果需要更长的随机名，可以调整这个函数)
function generateRandomName(length = 8) {
    return randomBytes(Math.ceil(length / 2)).toString('hex').slice(0, length);
}


async function main() {
    let adminClient;
    let appClient;
    try {
        // --- 步骤 1: 使用管理员账户连接并创建用户 ---
        adminClient = new MongoClient(adminConnectionString);
        await adminClient.connect();
        console.log("已使用管理员账户连接到 MongoDB");

        const adminDb = adminClient.db('admin'); // 用户必须在 admin 数据库中创建

        // 检查用户是否已存在
        const existingUser = await adminDb.command({
            usersInfo: { user: appUser, db: appDatabase }
        });

        if (existingUser.users.length > 0) {
            console.log(`用户 "${appUser}" 在数据库 "${appDatabase}" 中已存在，跳过创建。`);
        } else {
            // 创建用户，并赋予其对特定数据库的读写权限
            // 注意：roles 中的 db 必须是目标数据库名
            await adminDb.command({
                createUser: appUser,
                pwd: appPassword,
                roles: [
                    {
                        role: "readWrite",
                        db: appDatabase
                    }
                ]
            });
            console.log(`成功创建用户 "${appUser}" 并授予其对数据库 "${appDatabase}" 的读写权限。`);
        }

        // --- 步骤 2: 验证新用户是否可以连接并操作其数据库 ---
        // 这是你的想法：如果用户创建失败，这一步就不会执行。
        const appConnectionString = `mongodb://${appUser}:${appPassword}@custom-db-dev-mongodb.ns-o5e3hy6h.svc:27017/${appDatabase}?replicaSet=custom-db-dev-mongodb`;
        
        appClient = new MongoClient(appConnectionString);
        await appClient.connect();
        console.log(`成功使用新用户 "${appUser}" 连接到数据库 "${appDatabase}"`);

        // 执行一个写操作来验证权限
        const db = appClient.db(appDatabase);
        const testCollection = db.collection('test');
        await testCollection.insertOne({ message: "Hello from new user!", createdAt: new Date() });
        console.log("成功插入测试文档，权限验证通过！");

    } catch (error) {
        console.error("发生错误:", error);
    } finally {
        // 确保所有连接都被关闭
        if (adminClient) {
            await adminClient.close();
            console.log("管理员连接已关闭");
        }
        if (appClient) {
            await appClient.close();
            console.log("应用用户连接已关闭");
        }
    }
}

// --- 执行主函数 ---
console.log(`本次运行将创建:`);
console.log(`- 数据库名: ${appDatabase}`);
console.log(`- 用户名:   ${appUser}`);
console.log(`- 密码:     ${appPassword}`);
console.log("------------------------------------");

main().catch(console.error);
