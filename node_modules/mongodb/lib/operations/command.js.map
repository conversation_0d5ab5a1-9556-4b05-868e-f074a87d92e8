{"version": 3, "file": "command.js", "sourceRoot": "", "sources": ["../../src/operations/command.ts"], "names": [], "mappings": ";;;AAEA,oCAAqD;AACrD,wCAKoB;AACpB,kDAA8C;AAG9C,+DAA4E;AAG5E,oCAAwF;AACxF,oDAA0E;AAE1E,2CAA+E;AA4D/E,gBAAgB;AAChB,MAAsB,gBAAoB,SAAQ,6BAAoB;IAMpE,YAAY,MAAwB,EAAE,OAAiC;QACrE,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAE7B,kFAAkF;QAClF,kFAAkF;QAClF,qBAAqB;QACrB,MAAM,cAAc,GAAG,OAAO,EAAE,MAAM,IAAI,OAAO,EAAE,MAAM,CAAC;QAC1D,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,EAAE,GAAG,IAAI,wBAAgB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,EAAE,GAAG,MAAM;gBACd,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC;gBAC3C,CAAC,CAAC,IAAI,wBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,0BAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,YAAY,GAAG,4BAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,WAAW,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,OAAO,GAAG,iBAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,IAAI,CAAC,OAAO;gBAAE,IAAA,uCAA6B,EAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9E,CAAC;aAAM,IAAI,OAAO,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC;YACpC,MAAM,IAAI,iCAAyB,CAAC,mDAAmD,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAED,IAAa,aAAa;QACxB,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,WAAW,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;QAC9B,CAAC;QACD,OAAO,KAAK,CAAC,aAAa,CAAC;IAC7B,CAAC;IAiBD,KAAK,CAAC,cAAc,CAClB,MAAc,EACd,OAAkC,EAClC,GAAa,EACb,cAA8B,EAC9B,YAAyC;QAEzC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,MAAM,OAAO,GAAG;YACd,GAAG,IAAI,CAAC,OAAO;YACf,GAAG,IAAI,CAAC,WAAW;YACnB,cAAc;YACd,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,OAAO;SACR,CAAC;QAEF,MAAM,iBAAiB,GAAG,IAAA,sBAAc,EAAC,MAAM,CAAC,CAAC;QACjD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAEnE,IAAI,IAAI,CAAC,WAAW,IAAI,IAAA,kCAA0B,EAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC1E,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,GAAG,mDAAgC,EAAE,CAAC;YACnF,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAClF,4BAAY,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC7C,CAAC;QAED,IACE,OAAO,CAAC,SAAS;YACjB,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ;YACrC,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,cAAc,CAAC,EACtC,CAAC;YACD,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YAC1C,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAM,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACvD,GAAG,GAAG,IAAA,6BAAmB,EAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;IACnE,CAAC;CACF;AAzGD,4CAyGC"}