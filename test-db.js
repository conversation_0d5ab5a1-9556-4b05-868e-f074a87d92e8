const { MongoClient } = require('mongodb');

const uri = 'mongodb://root:<EMAIL>:27017/?replicaSet=custom-db-dev-mongodb';

const client = new MongoClient(uri);

async function run() {
  try {
    await client.connect();
    console.log('已连接到 MongoDB Replica Set');

    // 指定新数据库（例如 'myDatabase'）
    const db = client.db('myDatabase');

    // 插入数据以创建集合
    const collection = db.collection('myCollection');
    await collection.insertOne({ name: '示例数据', timestamp: new Date() });
    console.log('已插入数据到 myDatabase.myCollection');

    // 列出集合以验证
    const collections = await db.listCollections().toArray();
    console.log('集合列表:', collections.map(c => c.name));

    // 列出所有数据库以确认新数据库创建
    const adminDb = client.db('admin');
    const { databases } = await adminDb.admin().listDatabases();
    console.log('数据库列表:', databases.map(db => db.name));
  } finally {
    await client.close();
  }
}
run().catch(console.error);